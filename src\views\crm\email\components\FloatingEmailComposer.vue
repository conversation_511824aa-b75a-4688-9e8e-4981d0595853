<template>
  <div
    v-if="visible"
    class="floating-email-composer"
    :style="windowStyle"
    @mousedown="handleWindowMouseDown"
  >
    <!-- 标题栏 -->
    <div
      class="floating-header"
      @mousedown="handleHeaderMouseDown"
      @mouseup="handleHeaderMouseUp"
      @dblclick="handleMaximize"
    >
      <div class="floating-title">
        <span>{{ windowData.composeData.subject || '新邮件' }}</span>
      </div>
      <div class="floating-controls">
        <button
          class="control-btn minimize-btn"
          @click="handleMinimize"
          title="最小化"
        >
          <MinimizeIcon class="icon-small" />
        </button>
        <button
          class="control-btn maximize-btn"
          @click="handleMaximize"
          :title="isMaximized ? '还原' : '最大化'"
        >
          <Minimize2Icon v-if="isMaximized" class="icon-small" />
          <MaximizeIcon v-else class="icon-small" />
        </button>
        <button
          class="control-btn close-btn"
          @click="handleClose"
          title="关闭"
        >
          <XIcon class="icon-small" />
        </button>
      </div>
    </div>

    <!-- 邮件编辑器内容 -->
    <div class="floating-content">
      <EmailComposer
        ref="emailComposer"
        :initial-data="windowData.composeData || {}"
        :compose-mode="windowData.composeMode || 'compose'"
        :replying-to="windowData.replyingTo"
        :show-compose="true"
        @close="handleClose"
        @send="handleSend"
        @save="handleSave"
      />
    </div>

    <!-- 调整大小的拖拽点 -->
    <div class="resize-handles">
      <div class="resize-handle resize-n" @mousedown="handleResizeMouseDown('n', $event)"></div>
      <div class="resize-handle resize-s" @mousedown="handleResizeMouseDown('s', $event)"></div>
      <div class="resize-handle resize-e" @mousedown="handleResizeMouseDown('e', $event)"></div>
      <div class="resize-handle resize-w" @mousedown="handleResizeMouseDown('w', $event)"></div>
      <div class="resize-handle resize-ne" @mousedown="handleResizeMouseDown('ne', $event)"></div>
      <div class="resize-handle resize-nw" @mousedown="handleResizeMouseDown('nw', $event)"></div>
      <div class="resize-handle resize-se" @mousedown="handleResizeMouseDown('se', $event)"></div>
      <div class="resize-handle resize-sw" @mousedown="handleResizeMouseDown('sw', $event)"></div>
    </div>
  </div>
</template>

<script>
import { X, Minimize, Maximize, Minimize2 } from 'lucide-vue'
import EmailComposer from './EmailComposer.vue'

export default {
  name: 'FloatingEmailComposer',
  components: {
    XIcon: X,
    MinimizeIcon: Minimize,
    MaximizeIcon: Maximize,
    Minimize2Icon: Minimize2,
    EmailComposer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    windowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 窗口位置和大小
      position: {
        x: 50,
        y: 50
      },
      size: {
        width: 1200,
        height: 800
      },
      minSize: {
        width: 600,
        height: 400
      },
      // 拖拽状态
      isDragging: false,
      isResizing: false,
      resizeDirection: '',
      dragStart: {
        x: 0,
        y: 0,
        windowX: 0,
        windowY: 0,
        windowWidth: 0,
        windowHeight: 0
      },
      // 窗口状态
      isMinimized: false,
      isMaximized: false,
      // 保存最大化前的窗口状态
      beforeMaximize: {
        position: { x: 0, y: 0 },
        size: { width: 0, height: 0 }
      }
    }
  },
  computed: {
    windowStyle() {
      return {
        left: `${this.position.x}px`,
        top: `${this.position.y}px`,
        width: `${this.size.width}px`,
        height: this.isMinimized ? '40px' : `${this.size.height}px`,
        zIndex: 9999
      }
    }
  },
  mounted() {
    // 初始化窗口大小和位置
    this.initializeWindow()

    // 添加全局事件监听
    document.addEventListener('mousemove', this.handleGlobalMouseMove)
    document.addEventListener('mouseup', this.handleGlobalMouseUp)
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    // 移除全局事件监听
    document.removeEventListener('mousemove', this.handleGlobalMouseMove)
    document.removeEventListener('mouseup', this.handleGlobalMouseUp)
    window.removeEventListener('resize', this.handleWindowResize)
  },
  methods: {
    // 初始化窗口大小和位置
    initializeWindow() {
      this.calculateOptimalSize()
      this.centerWindow()
    },

    // 计算最佳窗口大小
    calculateOptimalSize() {
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // 计算最佳宽度（屏幕宽度的80%，但不超过1400px，不小于800px）
      const optimalWidth = Math.min(
        Math.max(viewportWidth * 0.8, 800),
        1400
      )

      // 计算最佳高度（屏幕高度的85%，但不超过900px，不小于600px）
      const optimalHeight = Math.min(
        Math.max(viewportHeight * 0.85, 600),
        900
      )

      this.size.width = optimalWidth
      this.size.height = optimalHeight
    },

    // 居中显示窗口
    centerWindow() {
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      this.position.x = Math.max(20, (viewportWidth - this.size.width) / 2)
      this.position.y = Math.max(20, (viewportHeight - this.size.height) / 2)
    },

    // 标题栏鼠标按下事件
    handleHeaderMouseDown(event) {
      if (event.target.closest('.floating-controls')) {
        return // 如果点击的是控制按钮，不处理拖拽
      }

      this.isDragging = true
      this.dragStart.x = event.clientX
      this.dragStart.y = event.clientY
      this.dragStart.windowX = this.position.x
      this.dragStart.windowY = this.position.y

      event.preventDefault()
    },

    // 标题栏鼠标抬起事件
    handleHeaderMouseUp() {
      this.isDragging = false
    },

    // 窗口鼠标按下事件
    handleWindowMouseDown(event) {
      // 将窗口置于最前
      this.$el.style.zIndex = 10000
    },

    // 调整大小鼠标按下事件
    handleResizeMouseDown(direction, event) {
      this.isResizing = true
      this.resizeDirection = direction
      this.dragStart.x = event.clientX
      this.dragStart.y = event.clientY
      this.dragStart.windowX = this.position.x
      this.dragStart.windowY = this.position.y
      this.dragStart.windowWidth = this.size.width
      this.dragStart.windowHeight = this.size.height

      event.preventDefault()
      event.stopPropagation()
    },

    // 全局鼠标移动事件
    handleGlobalMouseMove(event) {
      if (this.isDragging) {
        this.handleDrag(event)
      } else if (this.isResizing) {
        this.handleResize(event)
      }
    },

    // 全局鼠标抬起事件
    handleGlobalMouseUp() {
      this.isDragging = false
      this.isResizing = false
      this.resizeDirection = ''
    },

    // 处理拖拽
    handleDrag(event) {
      const deltaX = event.clientX - this.dragStart.x
      const deltaY = event.clientY - this.dragStart.y

      let newX = this.dragStart.windowX + deltaX
      let newY = this.dragStart.windowY + deltaY

      // 限制窗口不能拖出视口
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      newX = Math.max(0, Math.min(newX, viewportWidth - this.size.width))
      newY = Math.max(0, Math.min(newY, viewportHeight - 40)) // 至少保留标题栏可见

      this.position.x = newX
      this.position.y = newY
    },

    // 处理调整大小
    handleResize(event) {
      const deltaX = event.clientX - this.dragStart.x
      const deltaY = event.clientY - this.dragStart.y

      let newWidth = this.dragStart.windowWidth
      let newHeight = this.dragStart.windowHeight
      let newX = this.dragStart.windowX
      let newY = this.dragStart.windowY

      // 根据调整方向计算新的尺寸和位置
      if (this.resizeDirection.includes('e')) {
        newWidth = Math.max(this.minSize.width, this.dragStart.windowWidth + deltaX)
      }
      if (this.resizeDirection.includes('w')) {
        newWidth = Math.max(this.minSize.width, this.dragStart.windowWidth - deltaX)
        if (newWidth > this.minSize.width) {
          newX = this.dragStart.windowX + deltaX
        }
      }
      if (this.resizeDirection.includes('s')) {
        newHeight = Math.max(this.minSize.height, this.dragStart.windowHeight + deltaY)
      }
      if (this.resizeDirection.includes('n')) {
        newHeight = Math.max(this.minSize.height, this.dragStart.windowHeight - deltaY)
        if (newHeight > this.minSize.height) {
          newY = this.dragStart.windowY + deltaY
        }
      }

      // 限制窗口不能超出视口
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      if (newX + newWidth > viewportWidth) {
        newWidth = viewportWidth - newX
      }
      if (newY + newHeight > viewportHeight) {
        newHeight = viewportHeight - newY
      }

      this.size.width = newWidth
      this.size.height = newHeight
      this.position.x = newX
      this.position.y = newY
    },

    // 最小化/还原
    handleMinimize() {
      this.isMinimized = !this.isMinimized
    },

    // 最大化/还原
    handleMaximize() {
      if (this.isMaximized) {
        // 还原窗口
        this.position.x = this.beforeMaximize.position.x
        this.position.y = this.beforeMaximize.position.y
        this.size.width = this.beforeMaximize.size.width
        this.size.height = this.beforeMaximize.size.height
        this.isMaximized = false
      } else {
        // 保存当前状态
        this.beforeMaximize.position.x = this.position.x
        this.beforeMaximize.position.y = this.position.y
        this.beforeMaximize.size.width = this.size.width
        this.beforeMaximize.size.height = this.size.height

        // 最大化窗口
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight

        this.position.x = 20
        this.position.y = 20
        this.size.width = viewportWidth - 40
        this.size.height = viewportHeight - 40
        this.isMaximized = true
      }
    },

    // 关闭窗口
    handleClose() {
      this.$emit('close')
    },

    // 邮件发送成功
    handleSend() {
      this.$emit('send')
      this.handleClose()
    },

    // 邮件保存成功
    handleSave() {
      this.$emit('save')
    },

    // 处理窗口大小变化
    handleWindowResize() {
      // 确保窗口不会超出屏幕边界
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // 调整位置，确保窗口完全可见
      if (this.position.x + this.size.width > viewportWidth) {
        this.position.x = Math.max(20, viewportWidth - this.size.width)
      }

      if (this.position.y + this.size.height > viewportHeight) {
        this.position.y = Math.max(20, viewportHeight - this.size.height)
      }

      // 如果窗口太大，调整大小
      if (this.size.width > viewportWidth - 40) {
        this.size.width = viewportWidth - 40
      }

      if (this.size.height > viewportHeight - 40) {
        this.size.height = viewportHeight - 40
      }
    }
  }
}
</script>

<style scoped>
.floating-email-composer {
  position: fixed;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  user-select: none;
  transition: box-shadow 0.2s ease;
}

.floating-email-composer:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 标题栏样式 */
.floating-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  color: white;
  cursor: move;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid #e1e5e9;
}

.floating-title {
  flex: 1;
  overflow: hidden;
  margin-right: 12px;
  font-size: 14px;
  font-weight: 500;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.floating-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 0;
  color: white;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn:hover {
  background: #ff4757;
}

.icon-small {
  width: 14px;
  height: 14px;
}

/* 内容区域样式 */
.floating-content {
  height: calc(100% - 40px);
  overflow: hidden;
  background: #ffffff;
}

/* 调整大小拖拽点样式 */
.resize-handles {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  background: transparent;
  pointer-events: all;
}

/* 边缘拖拽点 */
.resize-n {
  top: 0;
  right: 8px;
  left: 8px;
  height: 4px;
  cursor: n-resize;
}

.resize-s {
  right: 8px;
  bottom: 0;
  left: 8px;
  height: 4px;
  cursor: s-resize;
}

.resize-e {
  top: 8px;
  right: 0;
  bottom: 8px;
  width: 4px;
  cursor: e-resize;
}

.resize-w {
  top: 8px;
  bottom: 8px;
  left: 0;
  width: 4px;
  cursor: w-resize;
}

/* 角落拖拽点 */
.resize-ne {
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  cursor: ne-resize;
}

.resize-nw {
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  cursor: nw-resize;
}

.resize-se {
  right: 0;
  bottom: 0;
  width: 8px;
  height: 8px;
  cursor: se-resize;
}

.resize-sw {
  bottom: 0;
  left: 0;
  width: 8px;
  height: 8px;
  cursor: sw-resize;
}

/* 最小化状态样式 */
.floating-email-composer.minimized .floating-content {
  display: none;
}

.floating-email-composer.minimized .resize-handles {
  display: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .floating-email-composer {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border-radius: 0;
  }

  .resize-handles {
    display: none;
  }
}
</style>
