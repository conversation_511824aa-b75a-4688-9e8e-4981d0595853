<template>
  <div @open-floating-window="handleOpenFloatingWindow">
     <div  class="email-compose" v-loading="loading">
      <div class="compose-title">
          <h3 class="page-title">{{ composeData.subject || '新邮件' }}</h3>
      </div>
      <div class="compose-header">
        <div class="compose-actions-left">
          <button class="action-btn" @click="sendCompose">
            <SendIcon class="icon-small" />
            发送
            <ChevronDownIcon class="icon-small" />
          </button>
          <button class="action-btn" @click="previewCompose">预览</button>
          <button class="action-btn" @click="saveCompose">
            <SaveIcon class="icon-small" />
            保存
          </button>
          <button class="action-btn" @click="closeCompose">
            <XIcon class="icon-small" />
            关闭
          </button>
          <button class="action-btn" @click="openComposeMax">
            <XIcon class="icon-small" />
            新窗口打开
          </button>
        </div>
        <div class="compose-actions-right">
          <div class="compose-mode-indicator">
            <span v-if="composeMode === 'replynormal' || composeMode === 'replywithattachment' || composeMode === 'replywithoutoriginal'">回复</span>
            <span v-else-if="composeMode === 'replyAllnormal' || composeMode === 'replyAllwithattachment' || composeMode === 'replyAllwithoutoriginal'">回复全部</span>
            <span v-else-if="composeMode === 'forwardnormal' || composeMode === 'forwardwithoutoriginal'">转发</span>
            <span v-else>新邮件</span>
          </div>
          <div class="email-type-buttons">
            <button
              class="type-btn"
              :class="{ 'active': emailType === 1 }"
              @click="switchEmailType(1)"
            >
              普通邮件
            </button>
            <button
              class="type-btn"
              :class="{ 'active': emailType === 2 }"
              @click="switchEmailType(2)"
            >
              一对一邮件
            </button>
          </div>
        </div>
      </div>

      <div class="compose-form">
        <!-- 发件人行 - 包含千里眼、请求回执、优先级 -->
        <div class="form-row form-row-with-options">
          <div class="form-row-left">
            <label>发件人：</label>
            <div class="input-with-dropdown">
              <!-- <input type="text" v-model="composeData.from" placeholder="<EMAIL>" /> -->
                <el-select
                  filterable
                  remote
                  v-model="composeData.from"
                  placeholder="请选择邮箱"
                  :remote-method="handleRemoteSearch"
                  @focus="handleSelectFocus"
                  class="select-item">
                  <el-option
                    v-for="item in mailAccountList"
                    :key="item.userId"
                    :label="item.currentEmailAddress"
                    :value="item.currentEmailAddress" />
                </el-select>
              <ChevronDownIcon class="icon-small" />
            </div>
          </div>
          <div class="form-row-right">
            <div class="compose-options-inline">
              <button class="option-btn" :class="{ 'active': isEagleEyeActive }" @click="toggleEagleEye">
                <EyeIcon class="icon-small" />
                千里眼
              </button>
              <button class="option-btn">
                <CheckSquareIcon class="icon-small" />
                请求回执
              </button>
              <div class="priority-selector">
                <span class="priority-label">优先级</span>
                <div class="priority-buttons">
                  <button
                    class="option-btn"
                    :class="{ 'active': priorityValue ===1 }"
                    @click="priorityValue = 1"
                  >
                    普通
                  </button>
                  <button
                    class="option-btn urgent"
                    :class="{ 'active': priorityValue === 2 }"
                    @click="priorityValue = 2"
                  >
                    紧急
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 收件人行 - 包含抄送密送按钮 -->
        <div class="form-row form-row-with-options">
          <div class="form-row-left">
            <label>收件人：</label>
            <MultiEmailInput v-model="composeData.to"  :composeData="composeData"/>
          </div>
          <div class="form-row-right" v-if="emailType === 1">
            <div class="cc-bcc-buttons-inline">
              <button
                class="text-button"
                :class="{ 'active': showCc }"
                @click="toggleCc"
              >
                {{ showCc ? '隐藏抄送' : '添加抄送' }}
              </button>
              <button
                class="text-button"
                :class="{ 'active': showBcc }"
                @click="toggleBcc"
              >
                {{ showBcc ? '隐藏密送' : '添加密送' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 抄送和密送输入框 - 只在普通邮件模式下显示 -->
        <template v-if="emailType === 1">
          <!-- 抄送输入框 -->
          <div class="form-row cc-input-row" v-if="showCc">
            <label>抄送：</label>
            <MultiEmailInput v-model="composeData.cc" />
          </div>

          <!-- 密送输入框 -->
          <div class="form-row bcc-input-row" v-if="showBcc">
            <label>密送：</label>
            <MultiEmailInput v-model="composeData.bcc" />
          </div>
        </template>

        <div class="form-row">
          <label>主题：</label>
          <div class="subject-input">
            <el-input type="text" v-model="composeData.subject" placeholder="请输入邮件主题" style="width:90%"/>
            <!-- <CircleIcon class="icon-small" /> -->
            <!-- 已选标签显示区域 -->
            <div class="selected-tags-container" v-if="selectedTags.length > 0">
              <div
                v-for="(tag, index) in selectedTags"
                :key="`tag-${tag.id}`"
                class="selected-tag-item"
                :class="tag.type === 'custom' ? tag.colorClass : 'system'"
              >
                <span>{{ tag.name }}</span>
                <XIcon class="icon-tiny" @click="removeTag(tag)" />
              </div>
            </div>
            <div class="subject-actions">
              <button class="subject-action-btn" @click="toggleTagsDropdown">
                打标签
              </button>
                <div class="tags-dropdown" v-if="showTagsDropdown" @click.stop>
                  <div class="tags-list">
                    <div
                      v-for="(tag, index) in systemTags"
                      :key="`dropdown-${tag.id}`"
                      class="tag-item system-tag"
                      @click="addTag(tag)"
                    >
                      <span>{{ tag.name }}</span>
                    </div>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 附件上传和管理区域 -->
      <div class="compose-attachments" v-if="composeData.attachments && composeData.attachments.length > 0">
        <div class="attachments-header">
          <PaperclipIcon class="icon-small" /> 附件 ({{ composeData.attachments.length }})
          <span class="total-size">总大小: {{ getTotalAttachmentsSize() }}</span>
        </div>
        <div class="attachments-list">
          <div
            v-for="(attachment, index) in composeData.attachments"
            :key="index"
            class="attachment-item"
            :class="{ 'uploading': attachment.uploading }"
          >
            <div class="attachment-icon">
              <FileTextIcon v-if="isDocumentFile(attachment.name)" class="icon" />
              <ImageIcon v-else-if="isImageFile(attachment.name)" class="icon" />
              <FileIcon v-else class="icon" />
            </div>
            <div class="attachment-info">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
              <div class="upload-progress" v-if="attachment.uploading">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: attachment.progress + '%' }"></div>
                </div>
                <div class="progress-text">{{ attachment.progress }}%</div>
              </div>
            </div>
            <div class="attachment-actions">
              <!-- <EyeIcon
                v-if="canPreviewFile(attachment.name) && !attachment.uploading"
                class="icon-small"
                title="预览"
                @click.stop="previewAttachment(attachment)"
              /> -->
              <XIcon class="icon-small" title="移除" @click="removeAttachment(attachment,index,composeData.attachments)" />
            </div>
          </div>
        </div>
      </div>



      <div class="compose-toolbar">
        <div class="toolbar-group">
          <button class="toolbar-btn">
            <FileTextIcon class="icon-small" />
            模板
          </button>
          <label class="toolbar-btn file-upload-btn">
            <input
              type="file"
              multiple
              @change="handleFileUpload"
              ref="fileInput"
              class="hidden-file-input"
            />
            <PaperclipIcon class="icon-small" />
            附件
            <ChevronDownIcon class="icon-small" />
          </label>
          <button class="toolbar-btn" @click="handleInsertImage">
            <ImageIcon class="icon-small" />
            插入图片
          </button>
          <div class="signature-btn-container">
            <button class="toolbar-btn" @click="handleInsertSignature">
              <Highlighter class="icon-small" />
              插入签名
            </button>
            <div v-if="showSignatureMenu" class="signature-menu">
              <ul>
                <li
                  v-for="sig in signatureList"
                  :key="sig.name"
                  :style="{ color:'#333' }"
                  @click="selectSignature(sig.html)"
                >
                  {{ sig.name }}
                </li>
              </ul>
            </div>
          </div>
          <!-- <button class="toolbar-btn" @click="toggleAttachmentOptions">
            <ImageIcon class="icon-small" />
            超大附件
            <ChevronDownIcon class="icon-small" />
          </button>
          <div class="toolbar-dropdown" v-if="showAttachmentOptions">
            <div class="dropdown-item" @click="openFileUpload('normal')">
              <PaperclipIcon class="icon-small" /> 普通附件
            </div>
            <div class="dropdown-item" @click="openFileUpload('large')">
              <ImageIcon class="icon-small" /> 超大附件
            </div>
            <div class="dropdown-item" @click="openFileUpload('image')">
              <ImageIcon class="icon-small" /> 图片附件
            </div>
          </div>-->
          <button class="toolbar-btn">
            <HashIcon class="icon-small" />
            批注
          </button>
          <button class="toolbar-btn">
            <EyeIcon class="icon-small" />
            跟踪提醒
          </button>
          <button class="toolbar-btn">
            <FolderPlusIcon class="icon-small" />
            插入宏
          </button>
          <!-- <button class="toolbar-btn">
            <ShoppingBagIcon class="icon-small" />
            商品
          </button> -->
          <!-- <button class="toolbar-btn">
            <CodeIcon class="icon-small" />
            开发信
          </button> -->
          <button class="toolbar-btn">
            <FileIcon class="icon-small" />
            单据转附件
          </button>
          <!-- <button class="toolbar-btn">
            <CreditCardIcon class="icon-small" />
            PayPal收款
          </button> -->
        </div>
        <div class="toolbar-group">
          <button class="toolbar-btn">
            <TargetIcon class="icon-small" />
            测评
          </button>
          <button class="toolbar-btn">
            <ZapIcon class="icon-small" />
            AI 助手
          </button>
        </div>
      </div>
      <div class="meeting-panel" v-if="showMeetingPanel">
          <div class="panel-header">
            <h4>会议设置</h4>
            <XIcon class="icon-small close-icon" @click="showMeetingPanel = false" />
          </div>
          <div class="panel-body">
            <div class="meeting-form-row">
              <label>会议时间：</label>
              <div class="meeting-time-inputs">
                <el-date-picker
                  v-model="meetingStartTime"
                  type="datetime"
                  placeholder="开始时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                ></el-date-picker>
                <span class="time-separator">至</span>
                <el-date-picker
                  v-model="meetingEndTime"
                  type="datetime"
                  placeholder="结束时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                ></el-date-picker>
              </div>
            </div>
            <div class="meeting-form-row">
              <label>提醒频次：</label>
              <el-select v-model="meetingReminder" placeholder="请选择提醒频次">
                <el-option label="不提醒" value="none"></el-option>
                <el-option label="提前10分钟" value="10min"></el-option>
                <el-option label="提前30分钟" value="30min"></el-option>
                <el-option label="提前1小时" value="1hour"></el-option>
                <el-option label="提前1天" value="1day"></el-option>
              </el-select>
            </div>
            <div class="meeting-form-row">
              <label>会议地点：</label>
              <el-input v-model="meetingLocation" style="width:20%" placeholder="请输入会议地点"></el-input>
            </div>
          </div>
        </div>
      <div class="editor-container">
        <!-- 添加拖放功能的编辑器区域 -->
         <tinymce
          ref="createTinymce"
          v-model="composeData.content"
          :init="editorInit"
          :height="450"
          :toolbar="toolbar"
          :key="tinymceKey"
          class="rich-txt"
          @input="debouncedEditorInput" />


        <!-- 拖放提示覆盖层 -->
        <div class="drag-overlay" v-if="isDraggingOver">
          <div class="drag-message">
            <UploadCloudIcon class="large-icon" />
            <p>释放鼠标上传文件</p>
          </div>
        </div>
      </div>

      <div class="compose-footer">
        <div class="auto-save" :class="{
          'saving': autoSaveStatus === 'saving',
          'success': autoSaveStatus === 'success',
          'error': autoSaveStatus === 'error',
          'unsaved': hasUnsavedChanges && autoSaveStatus !== 'saving'
        }">
          {{ autoSaveStatusText || '等待编辑...' }}
        </div>
        <div class="email-size">预估邮件大小: {{ getEstimatedEmailSize() }}</div>
      </div>

      <!-- 邮件预览页面 -->
      <div class="email-preview-page" v-if="showEmailPreview">
        <div class="preview-header">
          <div class="header-left">
            <button class="back-button" @click="showEmailPreview = false">
              <span class="arrow-left">←</span> 返回编辑
            </button>
          </div>
          <!-- <div class="header-right">
            <button class="action-button" @click="showEmailPreview = false">知道了</button>
          </div> -->
        </div>
        <div class="preview-container">
          <div class="preview-sidebar">
            <div class="recipient-info">
              <div class="recipient-header">收件人</div>
              <div class="recipient-name">{{ composeData.to }}</div>
              <div class="recipient-count">1/1</div>
              <div class="recipient-nav">
                <button class="nav-btn prev" disabled>
                  <span>&lt;</span>
                </button>
                <button class="nav-btn next" disabled>
                  <span>&gt;</span>
                </button>
              </div>
            </div>
            <div class="recipient-details">
              <div class="detail-item">
                <div class="detail-label">发件人邮箱</div>
                <div class="detail-value">{{ composeData.from }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">收件人邮箱</div>
                <div class="detail-value">{{ composeData.to }}</div>
              </div>
            </div>
          </div>
          <div class="preview-content-area">
            <div class="email-preview-container">
              <div class="preview-email-header">
                <div class="field">
                  <span class="label">发件人：</span>
                  <span class="value">{{ composeData.from }}</span>
                </div>
                <div class="field">
                  <span class="label">收件人：</span>
                  <span class="value">{{ composeData.to }}</span>
                </div>
                <div class="field" v-if="composeData.cc">
                  <span class="label">抄送人：</span>
                  <span class="value">{{ composeData.cc }}</span>
                </div>
                <div class="field" v-if="composeData.bcc">
                  <span class="label">密送人：</span>
                  <span class="value">{{ composeData.bcc }}</span>
                </div>
                <div class="field">
                  <span class="label">主题：</span>
                  <span class="value">{{ composeData.subject || '无主题' }}</span>
                </div>
                <div class="field" v-if="composeData.attachments && composeData.attachments.length > 0">
                  <span class="label">附件：</span>
                  <span class="value">{{ composeData.attachments.length }}个附件</span>
                </div>
              </div>
              <div class="preview-email-content" v-html="composeData.content || '<p>无内容</p>'"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
</template>

<script>
import {
  X,
  Minimize,
  Paperclip,
  Clock,
  MoreVertical,
  Send,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Link,
  Image,
  ChevronDown,
  Save,
  Circle,
  Eye,
  FileText,
  File,
  CheckSquare,
  Hash,
  ShoppingBag,
  Code,
  CreditCard,
  Target,
  Zap,
  Type,
  Strikethrough,
  RotateCcw,
  RotateCw,
  AlignJustify,
  Indent,
  Table,
  Minus,
  Square,
  Smile,
  Frown,
  Film,
  Scissors,
  Layout,
  Link2,
  Columns,
  Maximize2,
  UploadCloud,
  Users,
  FolderPlus,
  Highlighter
} from 'lucide-vue';
import Tinymce from '@/components/Tinymce'
import { debounce } from 'throttle-debounce'
import { Message } from 'element-ui'
import { guid } from '@/utils/index'
import { formatFileSize, isDocumentFile, isImageFile, isPdfFile, isTextFile, getFileExtension, canPreviewFile } from '@/utils/format'
import { sendEmailAPI,queryMailAccountPageListAPI,saveEmailToDraftAPI,deleteEmailToDraftAPI } from '@/api/crm/email'
import { crmFileSaveAPI,crmFileDeleteAPI} from '@/api/common'
import MultiEmailInput from './MultiEmailInput.vue';
export default {
  name: 'EmailComposer',
  components: {
    XIcon: X,
    MinimizeIcon: Minimize,
    PaperclipIcon: Paperclip,
    ClockIcon: Clock,
    MoreVerticalIcon: MoreVertical,
    SendIcon: Send,
    BoldIcon: Bold,
    ItalicIcon: Italic,
    UnderlineIcon: Underline,
    AlignLeftIcon: AlignLeft,
    AlignCenterIcon: AlignCenter,
    AlignRightIcon: AlignRight,
    ListIcon: List,
    ListOrderedIcon: ListOrdered,
    LinkIcon: Link,
    ImageIcon: Image,
    ChevronDownIcon: ChevronDown,
    SaveIcon: Save,
    CircleIcon: Circle,
    EyeIcon: Eye,
    FileTextIcon: FileText,
    FileIcon: File,
    CheckSquareIcon: CheckSquare,
    HashIcon: Hash,
    ShoppingBagIcon: ShoppingBag,
    CodeIcon: Code,
    CreditCardIcon: CreditCard,
    TargetIcon: Target,
    ZapIcon: Zap,
    TypeIcon: Type,
    StrikethroughIcon: Strikethrough,
    RotateCcwIcon: RotateCcw,
    RotateCwIcon: RotateCw,
    AlignJustifyIcon: AlignJustify,
    IndentIcon: Indent,
    TableIcon: Table,
    MinusIcon: Minus,
    SquareIcon: Square,
    SmileIcon: Smile,
    FrownIcon: Frown,
    FilmIcon: Film,
    ScissorsIcon: Scissors,
    LayoutIcon: Layout,
    Link2Icon: Link2,
    ColumnsIcon: Columns,
    Maximize2Icon: Maximize2,
    UploadCloudIcon: UploadCloud,
    UsersIcon: Users,
    FolderPlusIcon: FolderPlus,
    Highlighter:Highlighter,
    Tinymce,
    MultiEmailInput
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: []
      })
    },
    // field: {
    //   type: Object,
    //   required: true
    // },
    composeMode: {
      type: String,
      default: 'new'
    },
    replyingTo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      subject: '',
      body: '',
      to: [],
      cc: [],
      bcc: [],
      showCc: false,
      showBcc: false,
      emailType: 1, // 'normal' 或 'oneToOne'
      // Remove redundant attachments array that's causing confusion
      field:{
        defaultValue:''
      },
      composeData: {
        from: '',
        senderId:'',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: []
      },
      rules: {
        to: [
          { required: true, message: '请输入至少一个有效邮箱地址', trigger: 'blur' }
        ]
      },
      internalComposeMode: this.composeMode || 'new', // 'new', 'reply', 'replyAll', 'forward'
      internalReplyingTo: this.replyingTo,
      currentView: 'compose',
      showAttachmentOptions: false,
      isDraggingOver: false,
      dragCounter: 0,
      previewingAttachment: null,
      previewUrl: '',
      previewContent: '',
      showPreviewModal: false,
      showEmailPreview: false,
      debouncedEditorInput: null,
      isEagleEyeActive: false,
      priorityValue:1,
      mailAccountList:[],
      fileList: [],
      // 标签相关
      selectedTags: [],
      systemTags: [
        { id: 'sys-1', name: '通知', type: 'system', colorClass: 'red' },
        { id: 'sys-2', name: '招聘', type: 'system', colorClass: 'red' },
        { id: 'sys-3', name: '商机', type: 'system', colorClass: 'red' },
        { id: 'sys-4', name: '报价', type: 'system', colorClass: 'red' },
        { id: 'sys-5', name: '已更回复', type: 'system', colorClass: 'red' },
        { id: 'sys-6', name: 'PI', type: 'system', colorClass: 'red' },
        { id: 'sys-7', name: '订单', type: 'system', colorClass: 'red' },
        { id: 'sys-8', name: '样品', type: 'system', colorClass: 'red' },
        { id: 'sys-9', name: '询盘', type: 'system', colorClass: 'red' }
      ],
      showTagsDropdown: false,
      // 会议相关
      showMeetingPanel: false,
      meetingStartTime: '',
      meetingEndTime: '',
      meetingReminder: 'none',
      meetingLocation: '',
      loading: false,
      fileBatchId:'',
      // 自动保存相关
      autoSaveTimer: null, // 定时器
      lastSavedTime: null, // 最后保存时间
      autoSaveStatus: '', // 自动保存状态：'saving', 'success', 'error'
      hasUnsavedChanges: false, // 是否有未保存的更改
      lastSavedContent: '', // 最后保存的内容，用于比较是否有变化
      periodicSaveTimer: null, // 定时自动保存定时器
      signatureList:[
        { name: '不使用签名', html: '' },
        { name: '默认签名', html: '    <br/><div style="font-family:Arial,sans-serif;font-size:14px;line-height:1.6;">' +'<p style="margin:0;">Best Regards</p>' +'<div style="height:1px;background:#ccc;margin:8px 0;"></div>' +'<a href="http://www.superbandmold.com" target="_blank">www.superbandmold.com</a>' +'</p></div>'},
      ],
      editorInstance:null,
      showSignatureMenu:false,
      editorInit:null,
      tinymceKey: Date.now(), // 用于强制重新渲染TinyMCE组件
      toolbar: [
        'undo redo',
        'formatpainter',
        'bold italic underline strikethrough',
        'fontselect fontsizeselect',
        'forecolor backcolor',
        'alignleft aligncenter alignright alignjustify',
        'outdent indent',
        'numlist bullist',
        'table',
        'link',
        'removeformat'
      ].join(' | ')
    };
  },
  created() {
    this.fileBatchId = guid();
    // 如果有初始数据，合并到composeData中
    if (this.initialData) {
      const processedData = { ...this.initialData }

      // 确保 to、cc、bcc 字段是数组类型
      if (processedData.to !== undefined) {
        processedData.to = this.convertEmailStringToArray(processedData.to)
      }
      if (processedData.cc !== undefined) {
        processedData.cc = this.convertEmailStringToArray(processedData.cc)
      }
      if (processedData.bcc !== undefined) {
        processedData.bcc = this.convertEmailStringToArray(processedData.bcc)
      }

      // if(this.initialData)

      this.composeData = {
        ...this.composeData,
        ...processedData
      };
    }

    // Initialize CC and BCC visibility based on data
    this.showCc = Array.isArray(this.composeData.cc) && this.composeData.cc.length > 0;
    this.showBcc = Array.isArray(this.composeData.bcc) && this.composeData.bcc.length > 0;

    this.debouncedEditorInput = debounce(300, this.editInputChange)
    // 添加点击事件监听器，用于关闭标签下拉菜单和会议面板
    this.getList();

    // 启动自动保存
    this.startPeriodicAutoSave();
    // 初始化最后保存的内容
    this.updateLastSavedContent();

    // 延迟设置初始内容快照，确保所有数据都已加载完成
    this.$nextTick(() => {
      setTimeout(() => {
        this.setInitialContent();
      }, 500); // 延迟500ms确保编辑器和数据都已完全初始化
    });
  },
  beforeDestroy() {
    // 移除事件监听器
    this.fileBatchId = null;
    // 清理自动保存定时器
    this.stopAutoSave();
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },
    // 自动保存状态显示
    autoSaveStatusText() {
      if (this.autoSaveStatus === 'saving') {
        return '正在保存...'
      } else if (this.autoSaveStatus === 'success' && this.lastSavedTime) {
        const time = new Date(this.lastSavedTime)
        const hours = String(time.getHours()).padStart(2, '0')
        const minutes = String(time.getMinutes()).padStart(2, '0')
        return `${hours}:${minutes} 自动保存成功`
      } else if (this.autoSaveStatus === 'error') {
        return '保存失败'
      } else if (this.hasUnsavedChanges) {
        return '有未保存的更改'
      }
      return ''
    }
  },
  watch: {
    // 监听initialData的变化
    initialData: {
      handler(newData, oldData) {
        if (newData) {
          // 检查是否只是附件数组发生了变化（由文件上传引起）
          if (oldData && this.isOnlyAttachmentsChanged(newData, oldData)) {
            // 如果只是附件变化，不重置其他数据，只更新附件
            return;
          }

          // 更新 composeData，确保账户信息同步和邮箱字段类型正确
          const processedData = { ...JSON.parse(JSON.stringify(newData)) }

          // 确保 to、cc、bcc 字段是数组类型
          if (processedData.to !== undefined) {
            processedData.to = this.convertEmailStringToArray(processedData.to)
          }
          if (processedData.cc !== undefined) {
            processedData.cc = this.convertEmailStringToArray(processedData.cc)
          }
          if (processedData.bcc !== undefined) {
            processedData.bcc = this.convertEmailStringToArray(processedData.bcc)
          }

          this.composeData = JSON.parse(JSON.stringify({
            ...this.composeData,
            ...processedData
          }))

          // 如果账户信息发生变化，需要重新获取邮箱列表并更新发件人信息
          if (newData.userId && newData.userId !== oldData?.userId) {
            this.getList();
          }

          this.showCc = Array.isArray(this.composeData.cc) && this.composeData.cc.length > 0;
          this.showBcc = Array.isArray(this.composeData.bcc) && this.composeData.bcc.length > 0;

          // 重新设置初始内容快照（延迟执行确保数据已更新）
          this.$nextTick(() => {
            setTimeout(() => {
              this.isInitialContentSet = false;
              this.setInitialContent();
            }, 100);
          });
        }
      },
      immediate: true,
      deep: true // 启用深度监听以捕获账户信息变化
    },
    // 监听composeMode的变化
    composeMode: {
      handler(newMode) {
        this.internalComposeMode = newMode || 'new';
      },
      immediate: true
    },
    // 监听邮件内容变化，用于自动保存
    'composeData.content': {
      handler(newContent, oldContent) {
        if (newContent !== oldContent && oldContent !== undefined) {
          this.hasUnsavedChanges = true;
          this.checkForAutoSave();
        }
      },
      deep: false
    },
    // 监听收件人变化
    'composeData.to': {
      handler() {
        this.hasUnsavedChanges = true;
        this.checkForAutoSave();
      },
      deep: true
    },
    // 监听主题变化
    'composeData.subject': {
      handler() {
        this.hasUnsavedChanges = true;
        this.checkForAutoSave();
      }
    },
    // 监听抄送变化
    'composeData.cc': {
      handler() {
        this.hasUnsavedChanges = true;
        this.checkForAutoSave();
      },
      deep: true
    },
    // 监听密送变化
    'composeData.bcc': {
      handler() {
        this.hasUnsavedChanges = true;
        this.checkForAutoSave();
      },
      deep: true
    }
  },
  mounted() {
  this.loadEditorConfig()
},
  methods: {
    // 从format.js导入的工具函数
    formatFileSize,
    isDocumentFile,
    isImageFile,
    isPdfFile,
    isTextFile,
    getFileExtension,
    canPreviewFile,
    loadEditorConfig() {
      // 延迟初始化配置，避免首次渲染时阻塞
      setTimeout(() => {
        this.editorInit = this.getEditConfig()
        // 强制重新渲染TinyMCE组件
        this.$nextTick(() => {
          this.tinymceKey = Date.now()
        })
      }, 0)
    },
    // 处理选择框焦点事件
    handleSelectFocus() {
      // 可以在这里添加焦点处理逻辑，比如加载邮箱列表等
    },

    selectSignature(signatureHTML) {
      if (this.editorInstance) {
        this.editorInstance.insertContent(signatureHTML);
      }
      this.showSignatureMenu = false;
    },

    // 处理远程搜索邮箱账户
    handleRemoteSearch(query) {
      // 可以在这里添加远程搜索逻辑，比如根据查询条件过滤邮箱列表
      if (query !== '') {
        // 这里可以调用API搜索邮箱账户
        // 暂时使用本地过滤作为示例
        // this.mailAccountList = this.mailAccountList.filter(item =>
        //   item.currentEmailAddress.toLowerCase().includes(query.toLowerCase())
        // );
      }
    },

    // 标签相关方法
    toggleTagsDropdown(event) {
      event.stopPropagation();
      this.showTagsDropdown = !this.showTagsDropdown;
    },

    addTag(tag) {
      // 检查标签是否已存在
      const exists = this.selectedTags.some(t => t.id === tag.id);
      if (!exists) {
        this.selectedTags.push({...tag});
      }
      this.showTagsDropdown = false; // 选择后关闭下拉菜单
    },

    removeTag(tag) {
      const index = this.selectedTags.findIndex(t => t.id === tag.id);
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
      }
    },

    getList() {
      let params = {
        current: 1,
        pages: 1000,
        type:'',
        smtp:'',
        userId:'',
        emailAddress:''
      };

      queryMailAccountPageListAPI(params)
        .then(res => {
          const {records} = res.data;
          records.forEach(item=>{
            if(item.outgoingAuthType == 2){
              item.currentEmailAddress = item.sendEmailAddress
            }else{
              item.currentEmailAddress = item.emailAddress
            }
          })
          this.mailAccountList = records;

          // 如果有指定的 userId，则设置对应的发件人信息
          if (this.composeData.userId) {
            const matchedAccount = records.find(item => item.userId === this.composeData.userId);
            if (matchedAccount) {
              this.composeData.from = matchedAccount.currentEmailAddress;
              this.composeData.userName = matchedAccount.userName;
              this.composeData.senderId = matchedAccount.id;
            }
          }else{
            const matchedAccount = records.find(item => item.userId === this.userInfo.userId);
            if(matchedAccount){
              this.composeData.from = matchedAccount.currentEmailAddress;
              this.composeData.userName = matchedAccount.userName;
              this.composeData.senderId = matchedAccount.id;
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    sendCompose() {
      if (!this.composeData.from) {
        this.$message.warning('发件人不能为空');
        return;
      }

      if(this.composeData.to.length === 0) {
        this.$message.warning('请至少输入一个收件人邮箱');
        return;
      }

      if (!this.composeData.subject) {
        this.$message.warning('主题不能为空');
        return;
      }

      if (this.composeData.subject && this.composeData.subject.length > 100) {
        this.$message.warning('主题长度不能超过100个字符');
        return;
      }

      // 确保bcc是数组类型再使用map函数
      const bccArray = Array.isArray(this.composeData.bcc) ? this.composeData.bcc :
                      (typeof this.composeData.bcc === 'string' && this.composeData.bcc ? [this.composeData.bcc] : []);
      let bccList = bccArray.length === 0 ? [] : bccArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      // 确保cc是数组类型再使用map函数
      const ccArray = Array.isArray(this.composeData.cc) ? this.composeData.cc :
                     (typeof this.composeData.cc === 'string' && this.composeData.cc ? [this.composeData.cc] : []);
      let ccList = ccArray.length === 0 ? [] : ccArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      // 确保to是数组类型再使用map函数
      const toArray = Array.isArray(this.composeData.to) ? this.composeData.to :
                     (typeof this.composeData.to === 'string' && this.composeData.to ? [this.composeData.to] : []);
      let toList = toArray.length === 0 ? [] : toArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      this.loading = true;

      let params = {
          bccList:bccList,
          ccList:ccList,
          toList:toList,
          subject: this.composeData.subject,
          content:this.composeData.content,
          sendEmailAddress:this.composeData.from,
          sendType:this.emailType,
          senderId:this.composeData.senderId,
          fileBatchId:this.composeData.attachments &&
             this.composeData.attachments[0] &&
             this.composeData.attachments[0].batchId
             ? this.composeData.attachments[0].batchId
             : (this.composeData.attachments &&
                this.composeData.attachments.length > 0
                ? this.fileBatchId
                : ''),
          priority:this.priorityValue,
          originalEmailId: this.composeData.replyMode !== '' ? this.composeData.originalEmailId : '', // 如果有原始邮件ID，则传递
          // trackEnabled: this.isEagleEyeActive,
      }

      // 🔥 新增：如果是草稿模式，添加草稿ID用于更新
      if (this.composeData.replyMode === 'draft' && this.composeData.draftId) {
        params.id = this.composeData.draftId;
      }


      try{
        sendEmailAPI(params)
          .then(res => {
            this.loading = false
            Message.success(`邮件已发送至 ${this.composeData.to}`);
            // 发送成功后关闭编辑器并返回邮件列表主页面
            this.handleClose();
          })
          .catch(() => {
            this.loading = false
          })
        }catch (err) {
          this.loading = false
        }
    },

    previewCompose() {
      // 直接在当前页面内显示预览
      this.showEmailPreview = true;
    },

    async saveCompose() {
      this.loading = true;
      try {
        await this.saveEmailToDraft(false); // false 表示是手动保存
        // 更新自动保存状态
        this.autoSaveStatus = 'success';
        this.lastSavedTime = new Date();
        this.hasUnsavedChanges = false;
        this.updateLastSavedContent();
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    closeCompose() {
      // 对于回复/回复全部/转发模式，如果用户未进行任何编辑，直接关闭
      if (this.isReplyOrForwardMode() && !this.hasUserMadeChanges()) {
        this.handleClose();
        return;
      }

      // 对于新邮件模式，如果用户未进行任何操作，也直接关闭
      if (this.internalComposeMode === 'new' && !this.hasUserMadeChanges()) {
        this.handleClose();
        return;
      }

      // 其他情况使用原有逻辑
      if(this.hasUnsavedChanges && this.hasContentToSave()){
        this.$confirm('此邮件已保存为草稿，是否需要保留？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.saveCompose();
          })
          .catch(() => {
            if(this.composeData.draftId){
                this.deleteEmail();
            }else{
                this.handleClose();
            }
          })
      }else{
        this.handleClose();
      }
    },

    openComposeMax(){
      // 触发事件，通知父组件打开浮动窗口
      this.$emit('open-floating-window', {
        composeData: JSON.parse(JSON.stringify(this.composeData)),
        emailType: this.emailType,
        priorityValue: this.priorityValue,
        selectedTags: [...this.selectedTags],
        showCc: this.showCc,
        showBcc: this.showBcc,
        isEagleEyeActive: this.isEagleEyeActive,
        composeMode: this.internalComposeMode,
        replyingTo: this.internalReplyingTo
      });
    },

    // 处理浮动窗口打开事件
    handleOpenFloatingWindow(windowData) {
      // 向上传递事件到父组件
      this.$emit('open-floating-window', windowData);
    },

    // 统一的关闭处理方法
    handleClose() {
      // 判断当前是否在独立的写邮件页面
      if (this.isInComposePage()) {
        // 在独立页面中，使用路由跳转返回邮件列表
        this.$router.push('/crm/email/subs/index');
      } else {
        // 在组件内视图切换模式下，触发close事件
        this.$emit('close');
      }
    },

    // 判断是否在独立的写邮件页面
    isInComposePage() {
      return this.$route && this.$route.path.includes('/crm/email/subs/compose');
    },
    toggleEagleEye() {
      this.isEagleEyeActive = !this.isEagleEyeActive;
    },

    deleteEmail(){
       deleteEmailToDraftAPI(this.composeData.draftId)
        .then(res => {
          this.handleClose();
        })
        .catch((error) => {
          this.handleClose();
          console.error('草稿箱丢弃失败', error);
        })
    },

    // 邮件类型切换
    switchEmailType(type) {
      this.emailType = type;

      // 切换到一对一邮件时，清空并隐藏抄送和密送
      if (type === 2) {
        this.composeData.cc = [];
        this.composeData.bcc = [];
        this.showCc = false;
        this.showBcc = false;
      }
    },

    // 切换抄送显示
    toggleCc() {
      this.showCc = !this.showCc;
      if (this.showCc) {
        this.$nextTick(() => {
          // 聚焦到抄送输入框
          if (this.$refs.ccInput) {
            this.$refs.ccInput.focus();
          }
        });
      } else {
        // 隐藏时清空抄送内容
        this.composeData.cc = [];
      }
    },

    // 切换密送显示
    toggleBcc() {
      this.showBcc = !this.showBcc;
      if (this.showBcc) {
        this.$nextTick(() => {
          // 聚焦到密送输入框
          if (this.$refs.bccInput) {
            this.$refs.bccInput.focus();
          }
        });
      } else {
        // 隐藏时清空密送内容
        this.composeData.bcc = [];
      }
    },
    getEditConfig() {
      return {
        menubar: false,
        statusbar: false,
        plugins: 'lists link image table paste code help wordcount',
        paste_data_images: true, // 允许粘贴图片
        paste_enable_default_filters: true, // 启用默认过滤器
        placeholder: '输入文字内容',
        content_style: ' * {color: #262626; margin: 0;} body { margin: 8px; font-size: 14px; }',
        paste_retain_style_properties: 'all', // 保留所有样式
        toolbar_mode: 'sliding',
        table_default_attributes: {
          border: '1'
        },
        table_default_styles: {
          'border-collapse': 'collapse',
          'width': '100%'
        },
        formats: {
          bold: { inline: 'strong' },
          italic: { inline: 'em' },
          underline: { inline: 'span', styles: { 'text-decoration': 'underline' } }
        },
        paste_preprocess: function(plugin, args) {
          // 只替换标题标签，不删除格式化标签
          var replaceTag = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
          replaceTag.forEach(tag => {
            var reg1 = new RegExp(`<${tag}>`, 'g')
            var reg2 = new RegExp(`</${tag}>`, 'g')
            args.content = args.content.replace(reg1, '<p>')
            args.content = args.content.replace(reg2, '</p>')
          })

          // 删除所有font标签
          args.content = args.content.replace(/<\/font>/ig, '').replace(/<font[^>]+>/ig, '')
        },
        setup:(editor) => {
            // 保存编辑器实例引用
            this.editorInstance = editor;

            // 格式刷相关变量
            let formatPainterActive = false;
            let copiedFormat = null;

            // 确保在编辑器初始化后注册按钮
            const registerFormatPainter = () => {
              // 检查按钮是否已经注册，避免重复注册
              try {
                if (!editor.ui.registry.getAll().buttons.formatpainter) {
                  // 添加自定义格式刷按钮
                  editor.ui.registry.addButton('formatpainter', {
                    text: '',
                    icon: 'format-painter',
                    tooltip: '格式刷 - 先选择要复制格式的文本，点击格式刷，再点击要应用格式的文本',
                    onAction: function() {
                      if (!formatPainterActive) {
                        // 激活格式刷模式
                        const selection = editor.selection;
                        const selectedNode = selection.getNode();

                        if (selectedNode && selectedNode.nodeType === 1) {
                          // 获取选中文本的格式
                          copiedFormat = {
                            fontFamily: editor.dom.getStyle(selectedNode, 'font-family'),
                            fontSize: editor.dom.getStyle(selectedNode, 'font-size'),
                            fontWeight: editor.dom.getStyle(selectedNode, 'font-weight'),
                            fontStyle: editor.dom.getStyle(selectedNode, 'font-style'),
                            textDecoration: editor.dom.getStyle(selectedNode, 'text-decoration'),
                            color: editor.dom.getStyle(selectedNode, 'color'),
                            backgroundColor: editor.dom.getStyle(selectedNode, 'background-color'),
                            textAlign: editor.dom.getStyle(selectedNode, 'text-align')
                          };

                          formatPainterActive = true;
                          editor.getBody().style.cursor = 'crosshair';

                          // 显示提示
                          // if (editor.notificationManager) {
                            // editor.notificationManager.open({
                            //   text: '格式刷已激活，点击要应用格式的文本',
                            //   type: 'info',
                            //   timeout: 3000
                            // });
                          // }

                        } else {
                          // if (editor.notificationManager) {
                          //   editor.notificationManager.open({
                          //     text: '请先选择要复制格式的文本',
                          //     type: 'warning',
                          //     timeout: 2000
                          //   });
                          // }
                        }
                      } else {
                        // 取消格式刷模式
                        formatPainterActive = false;
                        copiedFormat = null;
                        editor.getBody().style.cursor = 'auto';

                        // if (editor.notificationManager) {
                          // editor.notificationManager.open({
                          //   text: '格式刷已取消',
                          //   type: 'info',
                          //   timeout: 2000
                          // });
                        // }
                      }
                    }
                  });
                }
              } catch (error) {
                console.error('注册格式刷按钮失败:', error);
              }
            };

            // 立即尝试注册
            registerFormatPainter();

            // 监听点击事件应用格式
            editor.on('click', function(e) {
              if (formatPainterActive && copiedFormat) {
                const selection = editor.selection;
                const selectedNode = selection.getNode();

                if (selectedNode) {
                  // 应用复制的格式
                  Object.keys(copiedFormat).forEach(style => {
                    if (copiedFormat[style]) {
                      editor.dom.setStyle(selectedNode, style, copiedFormat[style]);
                    }
                  });

                  // 取消格式刷模式
                  formatPainterActive = false;
                  copiedFormat = null;
                  editor.getBody().style.cursor = 'auto';

                  // editor.notificationManager.open({
                  //   text: '格式已应用',
                  //   type: 'success',
                  //   timeout: 2000
                  // });
                }
              }
            });

            editor.on('init', function() {
              // 在编辑器初始化后再次尝试注册格式刷按钮
              setTimeout(() => {
                registerFormatPainter();
              }, 100);
            });
            editor.on('change', function() {
              console.log('内容已更改');
            });
        }
      }
    },
    editInputChange() {
      // 编辑器内容变化时触发
      this.hasUnsavedChanges = true;
      this.checkForAutoSave();
    },

    // 检查是否需要自动保存
    checkForAutoSave() {
      // 防抖处理，避免频繁触发
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      // 5秒后执行自动保存
      this.autoSaveTimer = setTimeout(() => {
        this.performAutoSave();
      }, 5000);
    },

    // 执行自动保存
    async performAutoSave() {
      // 检查是否有内容需要保存
      if (!this.hasContentToSave()) {
        console.log('自动保存跳过：无内容需要保存');
        return;
      }

      // 检查是否有变化
      if (!this.hasUnsavedChanges) {
        console.log('自动保存跳过：无未保存的更改');
        return;
      }

      // 对于回复/转发模式，如果用户未进行真正的编辑，不进行自动保存
      if (this.isReplyOrForwardMode() && !this.hasUserMadeChanges()) {
        return;
      }

      // 对于新邮件模式，如果用户未进行任何操作，也不进行自动保存
      if (this.internalComposeMode === 'new' && !this.hasUserMadeChanges()) {
        return;
      }

      this.autoSaveStatus = 'saving';

      try {
        const result = await this.saveEmailToDraft(true); // true 表示是自动保存
        console.log('自动保存成功，结果:', result);
        this.autoSaveStatus = 'success';
        this.lastSavedTime = new Date();
        this.hasUnsavedChanges = false;
        this.updateLastSavedContent();
      } catch (error) {
        this.autoSaveStatus = 'error';
      }
    },

    // 检查是否有内容需要保存
    hasContentToSave() {
      return (
        this.composeData.subject ||
        this.composeData.content ||
        (Array.isArray(this.composeData.to) && this.composeData.to.length > 0) ||
        (typeof this.composeData.to === 'string' && this.composeData.to.trim()) ||
        (Array.isArray(this.composeData.cc) && this.composeData.cc.length > 0) ||
        (typeof this.composeData.cc === 'string' && this.composeData.cc.trim()) ||
        (Array.isArray(this.composeData.bcc) && this.composeData.bcc.length > 0) ||
        (typeof this.composeData.bcc === 'string' && this.composeData.bcc.trim()) ||
        (this.composeData.attachments && this.composeData.attachments.length > 0)
      );
    },

    // 更新最后保存的内容
    updateLastSavedContent() {
      this.lastSavedContent = JSON.stringify({
        subject: this.composeData.subject,
        content: this.composeData.content,
        to: this.composeData.to,
        cc: this.composeData.cc,
        bcc: this.composeData.bcc,
        attachments: this.composeData.attachments
      });
    },

    // 保存邮件到草稿箱（统一的保存方法）
    async saveEmailToDraft(isAutoSave = false) {
      // 复用原有的saveCompose逻辑，但不显示成功消息和关闭窗口
      const bccArray = Array.isArray(this.composeData.bcc) ? this.composeData.bcc :
                      (typeof this.composeData.bcc === 'string' && this.composeData.bcc ? [this.composeData.bcc] : []);
      let bccList = bccArray.length === 0 ? [] : bccArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      const ccArray = Array.isArray(this.composeData.cc) ? this.composeData.cc :
                     (typeof this.composeData.cc === 'string' && this.composeData.cc ? [this.composeData.cc] : []);
      let ccList = ccArray.length === 0 ? [] : ccArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      const toArray = Array.isArray(this.composeData.to) ? this.composeData.to :
                     (typeof this.composeData.to === 'string' && this.composeData.to ? [this.composeData.to] : []);
      let toList = toArray.length === 0 ? [] : toArray.map(email => ({
        customerId:'',
        emailAddress:email,
        userId:''
      }));

      let params = {
          bccList:bccList,
          ccList:ccList,
          toList:toList,
          subject: this.composeData.subject,
          content:this.composeData.content,
          sendEmailAddress:this.composeData.from,
          sendType:this.emailType,
          senderId:this.composeData.senderId,
          fileBatchId:this.composeData.attachments &&
             this.composeData.attachments[0] &&
             this.composeData.attachments[0].batchId
             ? this.composeData.attachments[0].batchId
             : (this.composeData.attachments &&
                this.composeData.attachments.length > 0
                ? this.fileBatchId
                : ''),
          priority:this.priorityValue,
      }

      // 如果已经有草稿ID，则传递用于更新
      if (this.composeData.draftId) {
        params.emailId = this.composeData.draftId;
      }

      console.log('保存参数:', params);
      const res = await saveEmailToDraftAPI(params);
      console.log('保存响应:', res);

      // 更新草稿ID，支持多种响应格式
      if (res && res.data) {
          this.composeData.draftId = res.data;
      }

      console.log('更新后的 draftId:', this.composeData.draftId);

      if (!isAutoSave) {
        const message = this.composeData.replyMode === 'draft' ? '草稿已更新' : '邮件已保存到草稿箱';
        Message.success(message);
        this.handleClose();
      }

      return res;
    },

    // 启动定时自动保存（每分钟）
    startPeriodicAutoSave() {
      // 清除之前的定时器
      if (this.periodicSaveTimer) {
        clearInterval(this.periodicSaveTimer);
      }

      // 每60秒检查一次是否需要自动保存
      this.periodicSaveTimer = setInterval(() => {
        // 对于回复/转发模式，如果用户未进行真正的编辑，不进行定时自动保存
        if (this.isReplyOrForwardMode() && !this.hasUserMadeChanges()) {
          return;
        }

        // 对于新邮件模式，如果用户未进行任何操作，也不进行定时自动保存
        if (this.internalComposeMode === 'new' && !this.hasUserMadeChanges()) {
          return;
        }

        if (this.hasUnsavedChanges && this.hasContentToSave()) {
          this.performAutoSave();
        }
      }, 300000); // 60秒 = 1分钟
    },

    // 停止自动保存
    stopAutoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = null;
      }
      if (this.periodicSaveTimer) {
        clearInterval(this.periodicSaveTimer);
        this.periodicSaveTimer = null;
      }
    },

    // 重置邮件编辑器数据
    resetComposeData() {
      this.composeData = {
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: []
      };
    },

    // 切换附件选项
    toggleAttachmentOptions() {
      this.showAttachmentOptions = !this.showAttachmentOptions;
    },

    // 打开文件上传
    openFileUpload(type) {
      // 根据类型设置不同的上传参数
      this.showAttachmentOptions = false;
      this.$refs.fileInput.click();
    },

    // 处理拖拽事件
    handleDragEnter(e) {
      e.preventDefault();
      e.stopPropagation();
      this.dragCounter++;
      this.isDraggingOver = true;
    },

    handleDragOver(e) {
      e.preventDefault();
      e.stopPropagation();
    },

    handleDragLeave(e) {
      e.preventDefault();
      e.stopPropagation();
      this.dragCounter--;
      if (this.dragCounter <= 0) {
        this.isDraggingOver = false;
        this.dragCounter = 0;
      }
    },

    handleDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      this.isDraggingOver = false;
      this.dragCounter = 0;

      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        this.processFiles(Array.from(files));
      }
    },
    // 获取附件总大小
    getTotalAttachmentsSize() {
      if (!this.composeData.attachments || this.composeData.attachments.length === 0) {
        return '0 KB';
      }

      const totalBytes = this.composeData.attachments.reduce((total, attachment) => {
        return total + (attachment.size || 0);
      }, 0);

      return formatFileSize(totalBytes);
    },

    // 获取预估邮件大小
    getEstimatedEmailSize() {
      // 基础邮件大小（标题、收件人等元数据）
      let baseSize = 1024; // 1KB

      // 添加内容大小
      const contentSize = this.composeData.content ? this.composeData.content.length : 0;

      // 添加附件大小
      const attachmentsSize = this.composeData.attachments.reduce((total, attachment) => {
        return total + (attachment.size || 0);
      }, 0);

      const totalSize = baseSize + contentSize + attachmentsSize;
      return formatFileSize(totalSize);
    },

    // 处理文件上传
    handleFileUpload(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      this.processFiles(Array.from(files));
      // 重置文件输入，以便可以再次选择相同的文件
      event.target.value = null;
    },

    // 处理文件
    processFiles(files) {
      for (const file of files) {
        // 检查文件大小
        if (file.size > 10 * 1024 * 1024) { // 10MB
          alert(`文件 ${file.name} 太大，不能超过10MB`);
          continue;
        }

        this.uploadFileRequest(file)
      }
    },

    uploadFileRequest(file) {
      crmFileSaveAPI({ file: file, batchId: this.fileBatchId }).then(res => {
        const data = res.data || {}
        // 🔥 标记为新上传的附件，区分于来自原邮件的附件
        data.fromOriginalEmail = false
        this.composeData.attachments.push(data || {})
      }).catch((error) => {
        this.loading = false
      })
    },

    // 移除附件
    removeAttachment(attachment, index, attachments) {
      this.$confirm('您确定要删除该文件吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 🔥 修复：区分新上传的附件和来自原邮件的附件
          if (attachment.fromOriginalEmail) {
            // 来自原邮件的附件，只从列表中移除，不调用删除API
            this.composeData.attachments.splice(index, 1)
            this.$message.success('附件已移除')
          } else {
            // 新上传的附件，需要调用删除API
            crmFileDeleteAPI({
              id: attachment.fileId || attachment.id
            })
              .then(res => {
                this.composeData.attachments.splice(index, 1)
                this.$message.success('附件删除成功')
              })
              .catch((error) => {
                console.error('删除附件失败:', error)
                this.$message.error('删除附件失败，请重试')
              })
          }
        })
        .catch(() => {
        })
    },

    // 预览附件
    previewAttachment(attachment) {
      this.previewingAttachment = attachment;

      if (isImageFile(attachment.name)) {
        // 为图片创建一个临时URL
        if (attachment.file) {
          this.previewUrl = URL.createObjectURL(attachment.file);
        } else {
          // 模拟URL（已有的附件）
          this.previewUrl = '../assets/avatar.png';
        }
        this.previewContent = '';
      } else if (isPdfFile(attachment.name)) {
        // PDF预览
        if (attachment.file) {
          this.previewUrl = URL.createObjectURL(attachment.file);
        } else {
          this.previewUrl = '#';
          this.previewContent = '该附件格式不支持预览，请下载后查看';
        }
      } else if (isTextFile(attachment.name)) {
        // 文本文件预览
        if (attachment.file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            this.previewContent = e.target.result;
            this.previewUrl = '';
          };
          reader.readAsText(attachment.file);
        } else {
          this.previewContent = '该附件格式不支持预览，请下载后查看';
          this.previewUrl = '';
        }
      }

      this.showPreviewModal = true;
    },

    // 切换附件选项下拉菜单
    toggleAttachmentOptions() {
      this.showAttachmentOptions = !this.showAttachmentOptions;
    },

    // 打开文件选择对话框
    openFileUpload(type) {
      // 根据类型设置接受的文件类型
      if (type === 'image') {
        this.$refs.fileInput.accept = 'image/*';
      } else if (type === 'large') {
        this.$refs.fileInput.accept = '*/*';
      } else {
        this.$refs.fileInput.accept = '*/*';
      }

      // 触发文件选择对话框
      this.$refs.fileInput.click();
      this.showAttachmentOptions = false;
    },

     // 拖放相关方法
    // 处理拖拽进入事件
    handleDragEnter(event) {
      event.preventDefault();
      event.stopPropagation();
      this.dragCounter++;

      if (event.dataTransfer.items && event.dataTransfer.items.length > 0) {
        this.isDraggingOver = true;
      }
    },

    // 处理拖拽悬停事件
    handleDragOver(event) {
      event.preventDefault();
      event.stopPropagation();
    },

    // 处理拖拽离开事件
    handleDragLeave(event) {
      event.preventDefault();
      event.stopPropagation();
      this.dragCounter--;

      if (this.dragCounter === 0) {
        this.isDraggingOver = false;
      }
    },

        // 处理拖拽放下事件
    handleDrop(event) {
      event.preventDefault();
      event.stopPropagation();
      this.isDraggingOver = false;
      this.dragCounter = 0;

      const files = event.dataTransfer.files;
      if (files && files.length > 0) {
        this.processFiles(files);
      }
    },

    // 设置初始内容快照（用于判断用户是否真正编辑了内容）
    setInitialContent() {
      if (!this.isInitialContentSet) {
        this.initialContent = JSON.stringify({
          subject: this.composeData.subject || '',
          content: this.composeData.content || '',
          to: this.composeData.to || '',
          cc: this.composeData.cc || '',
          bcc: this.composeData.bcc || '',
          attachments: this.composeData.attachments || []
        });
        this.isInitialContentSet = true;
      }
    },

    // 判断用户是否真正编辑了内容（相对于初始内容）
    hasUserMadeChanges() {
      if (!this.isInitialContentSet) {
        return false;
      }

      const currentContent = JSON.stringify({
        subject: this.composeData.subject || '',
        content: this.composeData.content || '',
        to: this.composeData.to || '',
        cc: this.composeData.cc || '',
        bcc: this.composeData.bcc || '',
        attachments: this.composeData.attachments || []
      });

      const hasChanges = this.composeData.to != '' && (currentContent !== this.initialContent);

      return hasChanges;
    },

    // 检查是否只有附件发生了变化
    isOnlyAttachmentsChanged(newData, oldData) {
      if (!newData || !oldData) return false;

      // 创建数据副本，排除attachments字段进行比较
      const newDataWithoutAttachments = { ...newData };
      const oldDataWithoutAttachments = { ...oldData };
      delete newDataWithoutAttachments.attachments;
      delete oldDataWithoutAttachments.attachments;

      // 如果除了attachments之外的其他字段都相同，则认为只有附件发生了变化
      return JSON.stringify(newDataWithoutAttachments) === JSON.stringify(oldDataWithoutAttachments);
    },

    // 判断是否为回复/回复全部/转发模式
    isReplyOrForwardMode() {
      return ['replynormal', 'replywithattachment','replywithoutoriginal', 'replyAllnormal','replyAllwithattachment','replyAllwithoutoriginal','forwardnormal','forwardwithoutoriginal'].includes(this.internalComposeMode);
    },

    // 将邮箱字符串转换为数组格式
    convertEmailStringToArray(emailString) {
      if (!emailString) return []
      if (Array.isArray(emailString)) return emailString

      // 处理单个邮箱地址格式："Name <<EMAIL>>"
      const fullStringMatch = emailString.match(/^(.+)<([^>]+)>$/)
      if (fullStringMatch) {
        const email = fullStringMatch[2].trim()
        return this.isValidEmail(email) ? [email] : []
      }

      // 处理多个邮箱地址（逗号、分号、空格分隔）
      const emailsArray = emailString.split(/[,;\uff1b\uff0c\s\n]+/)
        .map(email => {
          const matches = email.match(/<([^>]+)>/)
          return matches ? matches[1].trim() : email.trim()
        })
        .filter(email => email && this.isValidEmail(email))

      return Array.from(new Set(emailsArray))
    },

    // 验证邮箱地址格式
    isValidEmail(email) {
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return regex.test(email)
    },

    // 处理插入图片按钮点击
    handleInsertImage() {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.click()
      input.onchange = async () => {
        const file = input.files[0]
        if (!file) return

        try {
          crmFileSaveAPI({
            type: 'img',
            file: file,
            isOnline: 1,
            batchId: guid()
          }).then((res) => {
            if (res?.data?.fileId) {
              const url = `${process.env.VUE_APP_BASE_API}/adminFile/downImg/${res.data.fileId}`
              if (this.editorInstance && url) {
                this.editorInstance.insertContent(`<img src="${url}" alt="image" />`)
              }else{
                console.error('图片地址无效:', url)
              }
            } else {
              console.error('上传失败：未返回正确格式的图片 URL', res)
            }
          }).catch((err) => {
            console.error('上传失败', err)
          })
        } catch (err) {
          console.error('图片上传异常', err)
        }
      }
    },

    // 处理插入签名按钮点击
    handleInsertSignature() {
      this.showSignatureMenu = !this.showSignatureMenu
    }
  }
}
</script>
<style scoped>
/* 撰写邮件视图样式 */
.email-compose {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

/* 笔记本屏幕适配 */
@media screen and (max-height: 900px) {
  .email-compose {
    height: calc(100vh - 20px);
    max-height: calc(100vh - 20px);
  }
}

@media screen and (max-height: 768px) {
  .email-compose {
    height: calc(100vh - 10px);
    max-height: calc(100vh - 10px);
  }
}
.compose-title{
    background: white;
  padding: 8px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.compose-header {
  padding: 8px 16px;
  background-color: white;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.compose-actions-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.compose-actions-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 6px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background-color: #f0f2f5;
  border-color: #c0c4cc;
}

.action-btn.primary {
  background-color: #0052CC;
  color: white;
  border-color: #0052CC;
}

.action-btn.primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.compose-mode-indicator {
  font-weight: 500;
  color: #606266;
  padding: 0 8px;
}

.type-btn {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #f5f7fa;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.type-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.type-btn.active {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.type-btn.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.email-type-buttons {
  display: flex;
  gap: 0;
}

.email-type-buttons .type-btn {
  border-radius: 0;
}

.email-type-buttons .type-btn:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-right: none;
}

.email-type-buttons .type-btn:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.compose-form {
  flex: 0 0 auto;
  padding: 8px 8px 0 8px;
  border-bottom: 1px solid #ebeef5;
}

/* 笔记本屏幕下减少表单区域的间距 */
@media screen and (max-height: 900px) {
  .compose-form {
    padding: 6px 8px 0 8px;
  }

  .form-row {
    margin-bottom: 8px;
  }

  .form-row-with-options {
    margin-bottom: 8px;
  }
}

.form-row {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
  transition: all 0.3s ease;
}

.form-row.slide-enter-active,
.form-row.slide-leave-active {
  transition: all 0.3s ease;
}

.form-row.slide-enter,
.form-row.slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.form-row label {
  width: 80px;
  color: #606266;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
}

.form-row input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-row input:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-row input:hover {
  border-color: #c0c4cc;
}

/* 带选项的表单行布局 */
.form-row-with-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  gap: 12px;
  transition: all 0.3s ease;
}

.form-row-left {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
}

.form-row-left label {
  width: 80px;
  color: #606266;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
}

.form-row-left input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.form-row-left input:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-row-left input:hover {
  border-color: #c0c4cc;
}

.form-row-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 内联选项样式 */
.compose-options-inline {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cc-bcc-buttons-inline {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-with-dropdown {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-dropdown input {
  flex: 1;
  padding-right: 30px;
}

.input-with-dropdown .icon-small {
  position: absolute;
  right: 10px;
  color: #909399;
  cursor: pointer;
}

/* 发件人选择框样式统一 */
.select-item {
  width: 100% !important;
}

.select-item :deep(.el-input__wrapper) {
  min-height: 38px;
  height: 38px;
  align-items: center;
}

.select-item :deep(.el-input__inner) {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
  height: 38px;
  line-height: 22px;
}

.select-item :deep(.el-input__inner:focus) {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.select-item :deep(.el-input__inner:hover) {
  border-color: #c0c4cc;
}

.subject-input {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.subject-input input {
  flex: 1;
  padding-right: 30px;
}

/* 主题输入框placeholder样式 - 与Element UI保持一致 */
.subject-input input::placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.subject-input input::-webkit-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.subject-input input::-moz-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.subject-input input:-ms-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.subject-input .icon-small {
  position: absolute;
  right: 10px;
  color: #909399;
  cursor: pointer;
}

/* 主题行按钮和标签样式 */
.subject-actions {
  display: flex;
  margin-left: 10px;
  gap: 10px;
  min-width: 160px;
}

.subject-action-btn {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  color: #606266;
  transition: all 0.2s;
}

.subject-action-btn:hover {
  background-color: #fff;
  color: #409eff;
  border-color: #409eff;
}

/* 已选标签容器 */
.selected-tags-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 6px;
  margin: 0 10px;
}

.selected-tag-item {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 16px;
  padding: 2px 2px 2px 6px;
  font-size: 12px;
  color: #606266;
}

.selected-tag-item.system {
  background-color: #f0f0f0;
  color: #606266;
  min-width: 50px;
}

.selected-tag-item.green { background-color: #67c23a; color: white; }
.selected-tag-item.purple { background-color: #9c27b0; color: white; }
.selected-tag-item.brown { background-color: #795548; color: white; }
.selected-tag-item.cyan { background-color: #00bcd4; color: white; }
.selected-tag-item.orange { background-color: #ff9800; color: white; }
.selected-tag-item.red { background-color: #f44336; color: white; }
.selected-tag-item.blue { background-color: #2196f3; color: white; }

.selected-tag-item .icon-tiny {
  margin-left: 4px;
  cursor: pointer;
  width: 12px;
  height: 12px;
}

/* 标签下拉菜单 */
.tags-dropdown {
  position: absolute;
  top: 100%;
  right: 80px;
  width: 100px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 10px;
  margin-top: 5px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.system-tag {
  background-color: #f0f0f0;
  color: #606266;
}

.system-tag:hover {
  background-color: #e0e0e0;
}

/* 会议设置面板 */
.meeting-panel {
  position: relative;
  top: 100%;
  left: 20px;
  width: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
  margin: 5px 0;
  flex: 0 0 auto;
}

/* 笔记本屏幕下优化会议面板 */
@media screen and (max-height: 900px) {
  .meeting-panel {
    margin: 3px 0;
  }

  .panel-body {
    padding: 10px;
  }

  .meeting-form-row {
    margin-bottom: 10px;
  }
}

@media screen and (max-height: 768px) {
  .meeting-panel {
    margin: 2px 0;
  }

  .panel-body {
    padding: 8px;
  }

  .meeting-form-row {
    margin-bottom: 8px;
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-icon {
  cursor: pointer;
  color: #909399;
}

.panel-body {
  padding: 15px;
}

.meeting-form-row {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.meeting-form-row label {
  width: 80px;
  text-align: right;
  margin-right: 10px;
  color: #606266;
}

.meeting-time-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-separator {
  color: #909399;
}

.panel-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #f0f0f0;
  gap: 10px;
}

.cancel-btn, .confirm-btn {
  padding: 6px 15px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #606266;
}

.confirm-btn {
  background-color: #1890ff;
  color: white;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.confirm-btn:hover {
  background-color: #40a9ff;
}

.cc-bcc-buttons {
  padding-left: 80px;
  margin-top: -8px;
  margin-bottom: 8px;
  animation: fadeIn 0.3s ease;
}

.text-button {
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  padding: 4px 8px;
  font-size: 13px;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  margin-right: 12px;
}

.text-button:hover {
  color: #66b1ff;
  background-color: rgba(64, 158, 255, 0.1);
}

.text-button:active {
  transform: translateY(1px);
}

.text-button.active {
  color: #67c23a;
  background-color: rgba(103, 194, 58, 0.1);
  font-weight: 500;
}

.text-button.active:hover {
  color: #85ce61;
  background-color: rgba(103, 194, 58, 0.15);
}

/* 抄送和密送输入框动画 */
.cc-input-row, .bcc-input-row {
  animation: slideDown 0.3s ease;
  transform-origin: top;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.cc-input-row label, .bcc-input-row label {
  width: 80px;
  color: #606266;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px) scaleY(0.8);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0) scaleY(1);
    max-height: 60px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.compose-attachments {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  flex: 0 0 auto;
}

/* 笔记本屏幕下减少附件区域间距 */
@media screen and (max-height: 900px) {
  .compose-attachments {
    padding: 8px 16px;
  }
}

@media screen and (max-height: 768px) {
  .compose-attachments {
    padding: 6px 16px;
  }
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}

.total-size {
  margin-left: auto;
  font-size: 13px;
  color: #909399;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.2s;
}

.attachment-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.attachment-item.uploading {
  background-color: #f0f9ff;
}

.attachment-icon {
  margin-right: 10px;
  color: #606266;
}

.attachment-info {
  flex: 1;
  min-width: 0;
}

.attachment-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.attachment-size {
  font-size: 12px;
  color: #909399;
}

.upload-progress {
  margin-top: 4px;
}

.progress-bar {
  height: 4px;
  background-color: #ebeef5;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 2px;
}

.progress-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 2px;
}

.progress-text {
  font-size: 12px;
  color: #409eff;
  text-align: right;
}

.attachment-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.attachment-actions .icon-small {
  cursor: pointer;
  color: #606266;
  transition: color 0.2s;
}

.attachment-actions .icon-small:hover {
  color: #409eff;
}

.compose-options {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
}

.options-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.option-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.option-btn.active {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.option-btn .icon-small {
  margin-right: 4px;
}

.priority-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 13px;
}

.priority-buttons {
  display: flex;
  gap: 0;
}

.priority-buttons .option-btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

.priority-buttons .option-btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

/* 确保按钮连接处没有圆角 */
.priority-buttons .option-btn {
  border-radius: 0;
}

.priority-buttons .option-btn:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.priority-buttons .option-btn:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.option-btn.urgent {
  border-color: #f56c6c;
  color: #f56c6c;
}

.option-btn.urgent:hover {
  border-color: #f56c6c;
  color: #f56c6c;
  opacity: 0.8;
}

.option-btn.urgent.active {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
}

.priority-levels {
  display: flex;
  gap: 2px;
}

.priority-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.priority-label {
  font-size: 13px;
  color: #606266;
}

.priority-levels {
  display: flex;
  gap: 4px;
}

.priority-level {
  width: 16px;
  height: 16px;
  background-color: #dcdfe6;
  cursor: pointer;
  border-radius: 2px;
  transition: all 0.2s ease;
  position: relative;
}

.priority-level:hover {
  background-color: #c0c4cc;
}

.priority-level.active {
  background-color: #67c23a;
}

.priority-level.urgent.active {
  background-color: #f56c6c;
}

.priority-level.urgent {
  background-color: #fde2e2;
}

.priority-level.urgent:hover {
  background-color: #fecaca;
}

.compose-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 8px;
  flex: 0 0 auto;
}

/* 笔记本屏幕下减少工具栏间距 */
@media screen and (max-height: 900px) {
  .compose-toolbar {
    padding: 8px 16px;
    gap: 6px;
  }
}

@media screen and (max-height: 768px) {
  .compose-toolbar {
    padding: 6px 16px;
    gap: 4px;
  }
}

.toolbar-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  padding: 6px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.toolbar-btn:hover {
  border-color: #c0c4cc;
  color: #409eff;
}

.file-upload-btn {
  position: relative;
  overflow: hidden;
}

.hidden-file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.toolbar-dropdown {
  position: absolute;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
}

.dropdown-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

.editor-container {
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #dcdfe6;
  margin: 0 16px 16px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;
  min-height: 300px;
  height: auto;
}

/* 不同屏幕高度下的编辑器适配 */
@media screen and (min-height: 1080px) {
  .editor-container {
    min-height: 450px;
  }
}

@media screen and (max-height: 900px) {
  .editor-container {
    min-height: 250px;
    margin: 0 16px 8px;
  }
}

@media screen and (max-height: 768px) {
  .editor-container {
    min-height: 200px;
    margin: 0 16px 6px;
  }
}

@media screen and (max-height: 600px) {
  .editor-container {
    min-height: 150px;
    margin: 0 16px 4px;
  }
}

.editor-toolbar {
  padding: 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.font-family, .font-size {
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #606266;
  font-size: 13px;
}

.format-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.format-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: transparent;
  color: #606266;
  cursor: pointer;
  transition: all 0.2s;
}

.format-btn:hover {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

.editor-content {
  flex: 1;
  padding: 16px;
  min-height: 200px;
  outline: none;
  overflow-y: auto;
  line-height: 1.6;
  color: #303133;
}

.editor-content.drag-over {
  background-color: #f0f9ff;
  border: 2px dashed #409eff;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(64, 158, 255, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.drag-message {
  text-align: center;
  color: #409eff;
}

.large-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.compose-footer {
  padding: 12px 16px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  flex: 0 0 auto;
}

/* 笔记本屏幕下减少页脚间距 */
@media screen and (max-height: 900px) {
  .compose-footer {
    padding: 8px 16px;
  }
}

@media screen and (max-height: 768px) {
  .compose-footer {
    padding: 6px 16px;
  }
}

.auto-save {
  font-size: 12px;
  color: #909399;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.auto-save.saving {
  color: #409eff;
}

.auto-save.saving::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #409eff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.auto-save.success {
  color: #67c23a;
}

.auto-save.success::before {
  content: '✓';
  color: #67c23a;
  font-weight: bold;
  margin-right: 2px;
}

.auto-save.error {
  color: #f56c6c;
}

.auto-save.error::before {
  content: '✗';
  color: #f56c6c;
  font-weight: bold;
  margin-right: 2px;
}

.auto-save.unsaved {
  color: #e6a23c;
}

.auto-save.unsaved::before {
  content: '●';
  color: #e6a23c;
  margin-right: 2px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.email-size {
  color: #909399;
  font-size: 13px;
}

/* 确保SVG图标正确显示 */
::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

/* TinyMCE工具栏样式优化 - 解决图标被遮挡问题 */
::v-deep .tox-toolbar {
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2px;
  min-height: 44px;
}

::v-deep .tox-toolbar__primary {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2px;
  width: 100%;
}

::v-deep .tox-toolbar__group {
  display: flex;
  align-items: center;
  gap: 1px;
  padding: 2px;
  margin-right: 6px;
}

::v-deep .tox-toolbar__group:not(:last-child)::after {
  content: '';
  width: 1px;
  height: 24px;
  background-color: #e8e8e8;
  margin-left: 6px;
}

::v-deep .tox-tbtn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  border-radius: 3px;
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

::v-deep .tox-tbtn:hover {
  background-color: #f0f2f5;
  border-color: #d9d9d9;
}

::v-deep .tox-tbtn--enabled,
::v-deep .tox-tbtn[aria-pressed="true"] {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

::v-deep .tox-tbtn svg {
  width: 24px;
  height: 24px;
  display: block;
  fill: currentColor;
  flex-shrink: 0;
}

::v-deep .tox-tbtn .tox-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .tox-toolbar__overflow {
  display: flex;
  align-items: center;
}

/* 下拉按钮样式优化 */
::v-deep .tox-split-button {
  display: flex;
  align-items: center;
  border-radius: 3px;
  overflow: hidden;
  height: 34px;
}

::v-deep .tox-split-button__chevron {
  width: 18px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #e8e8e8;
  background-color: transparent;
  cursor: pointer;
}

::v-deep .tox-split-button__chevron:hover {
  background-color: #f0f2f5;
}

::v-deep .tox-split-button__chevron svg {
  width: 10px;
  height: 10px;
}

/* 字体和字号选择器样式 */
::v-deep .tox-selectfield {
  display: flex;
  align-items: center;
  height: 34px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  background-color: #fff;
  padding: 0 8px;
  min-width: 100px;
  max-width: 140px;
}

::v-deep .tox-selectfield select {
  border: none;
  background: transparent;
  outline: none;
  font-size: 13px;
  color: #333;
  width: 100%;
  height: 100%;
}

/* 颜色选择器样式 */
::v-deep .tox-colorpicker-button {
  position: relative;
  width: 34px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep .tox-colorpicker-button__color-preview {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  width: 18px;
  height: 3px;
  border-radius: 1px;
}

/* 表格按钮和其他特殊按钮样式 */
::v-deep .tox-tbtn--select {
  position: relative;
}

::v-deep .tox-tbtn--select::after {
  content: '';
  position: absolute;
  right: 2px;
  display: none;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 3px solid currentColor;
}
::v-deep .tox .tox-tbtn__select-chevron svg {
    fill: rgba(102, 102, 102, .5);
    height: 10px;
    width: 10px;
}

/* 列表按钮样式 */
::v-deep .tox-tbtn--bespoke {
  width: 34px;
  height: 34px;
}

/* 确保所有按钮文本和图标垂直居中 */
::v-deep .tox-tbtn__text {
  font-size: 12px;
  line-height: 1;
}

/* 工具栏分隔符 */
::v-deep .tox-toolbar__group + .tox-toolbar__group {
  padding-left: 6px;
  margin-left: 6px;
}

/* 移除默认的分隔符样式 */
::v-deep .tox-toolbar__group:not(:last-child)::after {
  display: none;
}

/* 编辑器容器样式 */
::v-deep .tox-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

::v-deep .tox-edit-area {
  border: none;
}

::v-deep .tox-edit-area iframe {
  border: none;
}
::v-deep .el-input__inner {
        border: 1px solid #DFE1E6 !important;
        height: 40px !important;
        padding: 0 10px !important;
      }

/* 邮件预览模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.preview-modal-content {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 800px;
  max-width: 95%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #606266;
  font-size: 14px;
  cursor: pointer;
  padding: 6px 10px;
}

.arrow-left {
  margin-right: 5px;
  font-size: 16px;
}

.action-button {
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 6px 16px;
  font-size: 14px;
  cursor: pointer;
}

.preview-modal-body {
  padding: 0;
  flex: 1;
  overflow: auto;
  max-height: 70vh;
  background-color: #f5f7fa;
}

.email-preview-container {
  padding: 16px;
  background-color: white;
  margin: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.preview-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.preview-header .field {
  margin-bottom: 8px;
  display: flex;
  font-size: 14px;
}

.preview-header .label {
  color: #909399;
  width: 70px;
  flex-shrink: 0;
}

.preview-header .value {
  color: #303133;
  font-weight: 500;
}

.preview-content {
  color: #303133;
  line-height: 1.6;
  font-size: 14px;
  padding: 0 8px;
}

.preview-content p {
  margin-bottom: 16px;
}

.preview-content img {
  max-width: 100%;
  height: auto;
}

/* 邮件预览页面样式 */
.email-preview-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f7fa;
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.preview-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.preview-sidebar {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.recipient-info {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.recipient-header {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.recipient-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recipient-count {
  font-size: 12px;
  color: #909399;
}

.recipient-nav {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
}

.nav-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.nav-btn:first-child {
  border-radius: 3px 0 0 3px;
}

.nav-btn:last-child {
  border-radius: 0 3px 3px 0;
  border-left: none;
}

.nav-btn:disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.recipient-details {
  padding: 16px;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
}

.preview-content-area {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.preview-email-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.preview-email-header .field {
  margin-bottom: 8px;
  display: flex;
}

.preview-email-header .label {
  color: #909399;
  width: 70px;
  flex-shrink: 0;
}

.preview-email-header .value {
  color: #303133;
  font-weight: 500;
}

.preview-email-content {
  color: #303133;
  line-height: 1.6;
  font-size: 14px;
}

.preview-email-content p {
  margin-bottom: 16px;
}

.preview-email-content img {
  max-width: 100%;
  height: auto;
}
</style>

<!-- 非scoped样式块，确保主题输入框placeholder样式生效 -->
<style>
.email-compose .subject-input input::placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.email-compose .subject-input input::-webkit-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.email-compose .subject-input input::-moz-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
  opacity: 1;
}

.email-compose .subject-input input:-ms-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

/* 为了确保兼容性，也为其他input添加相同样式 */
.email-compose .form-row input::placeholder,
.email-compose .form-row-left input::placeholder,
.email-compose .input-with-dropdown input::placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.email-compose .form-row input::-webkit-input-placeholder,
.email-compose .form-row-left input::-webkit-input-placeholder,
.email-compose .input-with-dropdown input::-webkit-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}

.email-compose .form-row input::-moz-placeholder,
.email-compose .form-row-left input::-moz-placeholder,
.email-compose .input-with-dropdown input::-moz-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
  opacity: 1;
}

.email-compose .form-row input:-ms-input-placeholder,
.email-compose .form-row-left input:-ms-input-placeholder,
.email-compose .input-with-dropdown input:-ms-input-placeholder {
  font-weight: 500 !important;
  color: #c0c4cc !important;
}
.signature-btn-container {
  position: relative;
  display: inline-block;
}

.signature-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  background: white;
  border: 1px solid #ddd;
  padding: 6px 12px;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  min-width: 120px;
  border-radius: 4px;
}
.signature-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.signature-menu li {
  padding: 4px 8px;
  cursor: pointer;
}
.signature-menu li:hover {
  background-color: #f5f5f5;
}

/* 整体响应式优化 - 确保在各种笔记本屏幕上都能正常使用 */
@media screen and (max-height: 1024px) {
  .email-compose {
    font-size: 14px;
  }
}

@media screen and (max-height: 900px) {
  .email-compose {
    font-size: 13px;
  }

  .compose-header {
    padding: 6px 16px;
  }

  .action-btn, .type-btn {
    padding: 4px 8px;
    font-size: 13px;
  }
}

@media screen and (max-height: 768px) {
  .email-compose {
    font-size: 12px;
  }

  .compose-header {
    padding: 4px 16px;
  }

  .action-btn, .type-btn {
    padding: 3px 6px;
    font-size: 12px;
  }

  .toolbar-btn {
    padding: 4px;
    font-size: 12px;
  }
}

/* 确保编辑器在小屏幕上仍然可用 */
@media screen and (max-height: 600px) {
  .compose-header {
    padding: 3px 12px;
  }

  .compose-form {
    padding: 4px 8px 0 8px;
  }

  .form-row, .form-row-with-options {
    margin-bottom: 6px;
  }

  .compose-toolbar {
    padding: 4px 12px;
  }

  .compose-footer {
    padding: 4px 12px;
  }
}
</style>
