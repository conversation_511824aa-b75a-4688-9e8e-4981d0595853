<template>
  <div class="email-compose-page">
    <div class="compose-content">
      <EmailComposer
        ref="emailComposer"
        :initial-data="composeData"
        :show-compose="true"
        @close="handleClose"
        @send="handleSend"
        @save="handleSave"
      />
    </div>
  </div>
</template>

<script>
import EmailComposer from './components/EmailComposer'
import { queryEmailDetailsAPI } from '@/api/crm/email' // 🔥 新增：导入邮件详情API

export default {
  name: 'EmailComposePage',
  components: {
    EmailComposer
  },
  data() {
    return {
      composeData: {
        from: '',
        to: [],
        cc: [],
        bcc: [],
        subject: '',
        content: '',
        attachments: [],
        replyMode: 'new',
        customerData: null,
        contactData: null
      }
    }
  },
  created() {
    this.initComposeData()
  },
  watch: {
    '$route'(to, from) {
      // 只有当路由参数真正发生变化时才重新初始化
      if (to.path !== from.path || JSON.stringify(to.query) !== JSON.stringify(from.query)) {
        this.initComposeData()
      }
    }
  },
  methods: {
    // 初始化写邮件数据
    initComposeData() {

      const { query, params } = this.$route

      if (query.draftId) {
        this.loadDraftData(query.draftId)
        return
      }

      // 从路由参数中获取预填充数据
      if (query.from) {
        // 如果from是邮箱地址，直接使用
        // 如果是"Name <<EMAIL>>" 格式，提取邮箱地址
        const emailMatch = query.from.match(/^(.*)<(.*)>$/);
        if (emailMatch) {
          this.composeData.from = emailMatch[2].trim();
        } else {
          this.composeData.from = query.from;
        }
      }

      if (query.to) {
        this.composeData.to = this.convertEmailStringToArray(query.to)
      }

      if (query.cc) {
        // 将抄送人数据转换为数组格式传递给 MultiEmailInput 组件
        this.composeData.cc = this.convertEmailStringToArray(query.cc)
      }

      if (query.bcc) {
        // 将密送人数据转换为数组格式传递给 MultiEmailInput 组件
        this.composeData.bcc = this.convertEmailStringToArray(query.bcc)
      }

      if (query.userId) {
        // 将用户ID直接传递给 EmailComposer 组件
        this.composeData.userId = query.userId
      }

      // 只有在subject为空或者是默认值时才设置新的subject
      if (query.subject) {
        try {
          this.composeData.subject = decodeURIComponent(query.subject)
        } catch (error) {
          // 如果解码失败，直接使用原始值
          this.composeData.subject = query.subject;
        }
      }

      if (query.content) {
        this.composeData.content = decodeURIComponent(query.content)
      }

      // 回复模式
      if (query.replyMode) {
        this.composeData.replyMode = query.replyMode
      }

      // 原邮件ID（用于回复/转发）
      if (query.originalEmailId) {
        this.composeData.originalEmailId = query.originalEmailId
      }

      if(query.writeType) {
        this.composeData.writeType = query.writeType
      }

      // 🔥 新增：处理附件信息
      if (query.attachments && query.attachments.length > 0) {
        try {
          // 解析附件JSON字符串
          const attachments = JSON.parse(decodeURIComponent(query.attachments))
          if (Array.isArray(attachments) && attachments.length > 0) {
            // 处理附件数据格式，确保与EmailComposer组件期望的格式一致
            this.composeData.attachments = attachments.map(attachment => ({
              id: attachment.id || attachment.fileId,
              batchId:attachment.batchId,
              name: attachment.name || attachment.fileName,
              size: attachment.size || attachment.fileSize,
              type: attachment.type || this.getFileTypeFromName(
                attachment.name || attachment.fileName
              ),
              url: attachment.url || attachment.fileUrl,
              // 标记为来自原邮件的附件，用于区分新上传的附件
              fromOriginalEmail: true,
              // 保留原始数据以备后用
              originalData: attachment
            }))
            console.log('加载附件信息:', this.composeData.attachments)
          }
        } catch (error) {
          console.error('解析附件信息失败:', error)
        }
      }
    },

    // 🔥 新增：加载草稿数据
    async loadDraftData(draftId) {
      try {
        console.log('加载草稿数据:', draftId)

        // 调用API获取草稿详情
        const response = await queryEmailDetailsAPI({ id: draftId })
        const draftData = response.data

        if (draftData) {
          // 填充草稿数据到编辑器
          this.composeData = {
            from: draftData.sendEmailAddress || '',
            to: draftData.toList
              ? draftData.toList.map(item => item.emailAddress)
              : [],
            cc: draftData.ccList
              ? draftData.ccList.map(item => item.emailAddress)
              : [],
            bcc: draftData.bccList
              ? draftData.bccList.map(item => item.emailAddress)
              : [],
            subject: draftData.subject || '',
            content: draftData.content || '',
            attachments: draftData.fileList || [],
            replyMode: 'draft', // 标记为草稿模式
            draftId: draftId, // 保存草稿ID用于更新
            userId: draftData.belongMailAccountUserId || '',
            customerData: null,
            contactData: null
          }

          console.log('草稿数据加载成功:', this.composeData)
        }
      } catch (error) {
        console.error('加载草稿数据失败:', error)
        this.$message.error('加载草稿失败，请重试')
        // 加载失败时返回邮件列表
        this.goBack()
      }
    },

    // 返回邮件列表
    goBack() {
      this.$router.push('/crm/email/subs/index')
    },

    // 处理关闭
    handleClose() {
      this.goBack()
    },

    // 处理发送
    handleSend(data) {
      console.log('发送邮件:', data)
      // 发送成功后返回邮件列表
      this.$message.success('邮件发送成功')
      this.goBack()
    },

    // 处理保存
    handleSave(data) {
      console.log('保存邮件:', data)
      this.$message.success('邮件已保存到草稿箱')
    },

    // 将邮箱字符串转换为数组格式
    convertEmailStringToArray(emailString) {
      if (!emailString) return []
      if (Array.isArray(emailString)) return emailString

      // 处理单个邮箱地址格式："Name <<EMAIL>>"
      const fullStringMatch = emailString.match(/^(.+)<([^>]+)>$/)
      if (fullStringMatch) {
        const email = fullStringMatch[2].trim()
        return this.isValidEmail(email) ? [email] : []
      }

      // 处理多个邮箱地址（逗号、分号、空格分隔）
      const emailsArray = emailString.split(/[,;；，\s\n]+/)
        .map(email => {
          const matches = email.match(/<([^>]+)>/)
          return matches ? matches[1].trim() : email.trim()
        })
        .filter(email => email && this.isValidEmail(email))

      return Array.from(new Set(emailsArray))
    },

    // 验证邮箱地址格式
    isValidEmail(email) {
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return regex.test(email)
    },

    // 🔥 新增：根据文件名获取文件类型
    getFileTypeFromName(fileName) {
      if (!fileName) return 'file'

      const ext = fileName.toLowerCase().split('.').pop()
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
      const docExts = [
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'
      ]
      const archiveExts = ['zip', 'rar', '7z']

      if (imageExts.includes(ext)) return 'image'
      if (docExts.includes(ext)) return 'document'
      if (archiveExts.includes(ext)) return 'archive'

      return 'file'
    }
  }
}
</script>

<style lang="scss" scoped>
.email-compose-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}
.header-actions {
  display: flex;
  gap: 12px;
}

.compose-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

// 覆盖EmailComposer的样式，使其适应独立页面
:deep(.email-compose) {
  height: 100%;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

:deep(.compose-body) {
  height: calc(100vh - 120px);
}
</style>