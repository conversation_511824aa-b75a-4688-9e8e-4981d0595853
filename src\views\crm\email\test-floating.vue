<template>
  <div class="test-floating-page">
    <h1>浮动写邮件窗口测试页面</h1>
    
    <div class="test-controls">
      <button class="test-btn" @click="openFloatingWindow">
        打开浮动写邮件窗口
      </button>
      
      <button class="test-btn" @click="openFloatingWindowWithData">
        打开带数据的浮动窗口
      </button>
    </div>

    <!-- 浮动写邮件窗口 -->
    <floating-email-composer
      :visible="showFloatingComposer"
      :window-data="floatingWindowData"
      @close="handleFloatingWindowClose"
      @send="handleFloatingWindowSend"
      @save="handleFloatingWindowSave"
    />
  </div>
</template>

<script>
import FloatingEmailComposer from './components/FloatingEmailComposer.vue'

export default {
  name: 'TestFloating',
  components: {
    FloatingEmailComposer
  },
  data() {
    return {
      showFloatingComposer: false,
      floatingWindowData: {}
    }
  },
  methods: {
    openFloatingWindow() {
      this.floatingWindowData = {
        composeData: {
          to: [],
          cc: [],
          bcc: [],
          subject: '测试邮件',
          content: '这是一个测试邮件内容',
          attachments: [],
          from: '<EMAIL>'
        },
        composeMode: 'compose',
        replyingTo: null
      };
      this.showFloatingComposer = true;
    },

    openFloatingWindowWithData() {
      this.floatingWindowData = {
        composeData: {
          to: ['<EMAIL>'],
          cc: ['<EMAIL>'],
          bcc: [],
          subject: '回复：重要邮件',
          content: '<p>这是一个带有预填数据的测试邮件</p><p>包含HTML格式的内容</p>',
          attachments: [],
          from: '<EMAIL>'
        },
        composeMode: 'reply',
        replyingTo: {
          id: '123',
          subject: '重要邮件'
        }
      };
      this.showFloatingComposer = true;
    },

    handleFloatingWindowClose() {
      this.showFloatingComposer = false;
      this.floatingWindowData = {};
    },

    handleFloatingWindowSend() {
      this.$message.success('邮件发送成功');
      this.handleFloatingWindowClose();
    },

    handleFloatingWindowSave() {
      this.$message.success('草稿保存成功');
    }
  }
}
</script>

<style scoped>
.test-floating-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.test-controls {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 40px;
}

.test-btn {
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.test-btn:hover {
  background: #5a6fd8;
}

.test-btn:active {
  background: #4c63d2;
}
</style>
